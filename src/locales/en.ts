export const en = {
  // Common
  common: {
    apply: 'Apply',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    update: 'Update',
    reset: 'Reset',
    add: 'Add',
    remove: 'Remove',
    close: 'Close',
    open: 'Open',
    enable: 'Enable',
    disable: 'Disable',
    show: 'Show',
    hide: 'Hide',
    yes: 'Yes',
    no: 'No',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
  },

  // Settings
  settings: {
    title: 'Settings',
    basicSettings: 'Basic Settings',
    clockSettings: 'Clock Settings',
    searchSettings: 'Search Engine',
    fastLinksSettings: 'Quick Links',
    backgroundSettings: 'Background',
    presetsSettings: 'Presets',
    listsSettings: 'Lists Settings',
    
    // Basic settings
    language: 'Interface Language',
    exportSettings: 'Export',
    importSettings: 'Import',
    theme: 'Theme',
    accentColor: 'Accent Color',
    borderRadius: 'Border Radius',
    cleanMode: 'Clean Mode',
    cleanModeDescription: 'Hides list open buttons',
    
    // Clock
    showClock: 'Show Clock',
    showSeconds: 'Show Seconds',
    showDate: 'Show Date',
    clockSize: 'Clock Size',
    clockColor: 'Clock and Date Color',
    
    // Search
    showSearch: 'Show Search',
    searchEngine: 'Search Engine',
    searchSize: 'Search Size',
    searchBackgroundColor: 'Search Background Color',
    searchBorderColor: 'Search Border Color',
    searchTextColor: 'Search Text Color',
    searchBackdropBlur: 'Search Background Blur',
    
    // Fast links
    showFastLinks: 'Show Quick Links',
    fastLinksColumns: 'Number of Columns',
    fastLinksSize: 'Quick Links Size',
    fastLinksBackdropBlur: 'Quick Links Background Blur',
    addFastLink: 'Add Quick Link',
    
    // Lists
    showLists: 'Show Link Lists',
    listsColumns: 'Number of Columns',
    listsBackgroundColor: 'Lists Background Color',
    listsBackdropBlur: 'Lists Background Blur',
    addNewList: 'Add New List',
    
    // Background
    backgroundType: 'Background Type',
    solidColor: 'Solid Color',
    gradient: 'Gradient',
    image: 'Image',
    brightness: 'Brightness',
    contrast: 'Contrast',
    saturation: 'Saturation',
    blur: 'Blur',
    hueRotate: 'Hue Rotate',
    sepia: 'Sepia',
    grayscale: 'Grayscale',
    invert: 'Invert',
    shadowOverlay: 'Shadow Overlay',
    parallaxEffect: 'Parallax Effect',
    autoSwitch: 'Auto Switch',
    switchInterval: 'Switch Interval',
    addImages: 'Add Images',
    uploadImages: 'Upload Images',
    
    // Presets
    presets: 'Presets',
    createPreset: 'Create Preset',
    presetName: 'Preset Name',
    noPresets: 'No presets created. Create your first preset for quick switching between settings.',
    renamePreset: 'Rename Preset',
    updatePreset: 'Update Preset with Current Settings',
    deletePreset: 'Delete Preset',
    
    // Reset and export
    resetAllColors: 'Reset All Colors to Accent',
    resetAllSettings: 'Reset All Settings',
  },

  // Lists
  lists: {
    newList: 'New List',
    listName: 'List Name',
    addLink: 'Add Link',
    linkName: 'Link Name',
    linkUrl: 'Link URL',
    editList: 'Edit List',
    deleteList: 'Delete List',
    listIcon: 'List Icon',
    listColor: 'List Color',
    hideIcons: 'Hide Link Icons',
    openInNewWindow: 'Open in New Window',
    copyLink: 'Copy Link',
    editLink: 'Edit Link',
    deleteLink: 'Delete Link',
  },

  // Fast links
  fastLinks: {
    newFastLink: 'New Quick Link',
    fastLinkName: 'Name',
    fastLinkUrl: 'URL',
    editFastLink: 'Edit Quick Link',
    deleteFastLink: 'Delete Quick Link',
    fastLinkColor: 'Quick Link Color',
  },

  // Search
  search: {
    placeholder: 'Search...',
    searchWith: 'Search with',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
  },

  // Errors and notifications
  errors: {
    invalidUrl: 'Invalid URL',
    nameRequired: 'Name is required',
    urlRequired: 'URL is required',
    fileUploadError: 'File upload error',
    settingsImportError: 'Settings import error',
    settingsExportError: 'Settings export error',
  },

  // Tooltips
  tooltips: {
    settings: 'Settings',
    addList: 'Add List',
    addFastLink: 'Add Quick Link',
    editItem: 'Edit',
    deleteItem: 'Delete',
    updateItem: 'Update',
    resetColor: 'Reset Color',
    openLink: 'Open Link',
    copyLink: 'Copy Link',
    dragToReorder: 'Drag to reorder',
    exportSettings: 'Export all settings to file',
    importSettings: 'Import settings from file',
  },

  // Time and date
  time: {
    seconds: 'sec',
    minutes: 'min',
    hours: 'h',
    days: 'd',
    months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
    weekdays: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  },

  // Units and sizes
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sec',
    minutes: 'min',
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
  },
};
