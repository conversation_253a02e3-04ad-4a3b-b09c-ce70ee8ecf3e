// English translation dictionary
export const en = {
  // General
  common: {
    apply: 'Apply',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    update: 'Update',
    reset: 'Reset',
    resetIcon: 'Reset Icon',
    add: 'Add',
    remove: 'Remove',
    close: 'Close',
    open: 'Open',
    enable: 'Enable',
    disable: 'Disable',
    show: 'Show',
    hide: 'Hide',
    yes: 'Yes',
    no: 'No',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
    create: 'Create',
    enterName: 'Enter name...',
    sidebar: 'Sidebar',
    sidebarDescription: 'Sidebar with settings and additional content',
  },

  // Settings
  settings: {
    title: 'Settings',
    description: 'Here will be extension settings (theme, appearance, options, etc.)',
    basicSettings: 'Basic Settings',
    clockSettings: 'Clock Settings',
    searchSettings: 'Search Engine',
    fastLinksSettings: 'Quick Links',
    backgroundSettings: 'Background',
    presetsSettings: 'Presets',
    listsSettings: 'Lists Settings',

    // Basic settings
    language: 'Interface Language',
    exportSettings: 'Export',
    importSettings: 'Import',
    theme: 'Theme',
    accentColor: 'Accent Color',
    borderRadius: 'Border Radius',
    cleanMode: 'Clean Mode',
    cleanModeDescription: 'Hides list open buttons',
    colorScheme: 'Color Scheme',
    borderRounding: 'Element Rounding',
    
    // Clock
    showClock: 'Show Clock',
    showSeconds: 'Show Seconds',
    showDate: 'Show Date',
    clockSize: 'Clock Size',
    clockColor: 'Clock and Date Color',
    
    // Search
    showSearch: 'Show Search',
    searchEngine: 'Search Engine',
    searchSize: 'Search Size',
    searchBackgroundColor: 'Search Background Color',
    searchBorderColor: 'Search Border Color',
    searchTextColor: 'Search Text Color',
    searchBackdropBlur: 'Search Background Blur',
    showSearchEngine: 'Show Search Engine',
    backgroundBlur: 'Background Blur',
    
    // Quick links
    showFastLinks: 'Show Quick Links',
    fastLinksColumns: 'Number of Columns',
    fastLinksSize: 'Quick Link Size',
    fastLinksBackdropBlur: 'Quick Link Background Blur',
    addFastLink: 'Add Quick Link',
    textColor: 'Title Text Color',
    backdropColor: 'Backdrop Color',
    iconBackgroundColor: 'Icon Background Color',
    colors: 'Colors',
    display: 'Display',
    hideIcons: 'Hide Icons',
    hideText: 'Hide Text',
    backgroundEffects: 'Icon Background Effects',

    // Lists
    showLists: 'Show Link Lists',
    listsColumns: 'Number of Columns',
    listsBackgroundColor: 'Lists Background Color',
    listsBackdropBlur: 'Lists Background Blur',
    addNewList: 'Add New List',
    listBackground: 'List Background',
    backgroundColor: 'Background Color',
    borderColor: 'Border Color',
    borderThickness: 'Border Thickness',
    hideBorder: 'Hide Border',
    hideBackground: 'Hide Background',
    separator: 'Separator',
    separatorColor: 'Separator Color',
    separatorThickness: 'Separator Thickness',
    hideSeparator: 'Hide Separator',
    colorsAndIcons: 'Colors and Icons',
    titleColor: 'Title Color',
    linkColor: 'Link Color',
    hideLinkIcons: 'Hide Link Icons',
    listGrid: 'List Grid',
    columnsCount: 'Number of Columns',
    listManagement: 'List Management',
    
    // Background
    backgroundType: 'Background Type',
    solidColor: 'Solid Color',
    gradient: 'Gradient',
    image: 'Image',
    brightness: 'Brightness',
    contrast: 'Contrast',
    saturation: 'Saturation',
    blur: 'Blur',
    hueRotate: 'Hue Rotate',
    sepia: 'Sepia',
    grayscale: 'Grayscale',
    invert: 'Invert',
    shadowOverlay: 'Shadow Overlay',
    parallaxEffect: 'Parallax Effect',
    autoSwitch: 'Auto Switch',
    switchInterval: 'Switch Interval',
    addImages: 'Add Images',
    uploadImages: 'Upload Images',
    addImage: 'Add Image',
    checking: 'Checking...',
    addRandomPhoto: 'Add random photo from internet',
    parallaxDescription: 'Background image will follow mouse movement',
    shadowBottom: 'Bottom Shadow',
    shadowDescription: 'Gradient shadow for better list visibility',
    intensity: 'Intensity:',
    height: 'Height:',
    gallery: 'Gallery',
    startAutoSwitch: 'Start automatic switching',
    stopAutoSwitch: 'Stop automatic switching',
    onLoad: 'On Load',
    daily: 'Daily',
    deleteImage: 'Delete Image',
    
    // Gradients
    gradientType: 'Gradient Type',
    addColor: 'Add Color',
    deleteColor: 'Delete Color',
    direction: 'Direction',
    position: 'Position',
    customCSS: 'CSS gradient (optional)',
    customCSSDescription: 'Enter CSS gradient string to apply instead of manual setup',
    backgroundFilters: 'Background Filters',
    resetFilters: 'Reset Filters',
    expandFilters: 'Expand Filters',
    collapseFilters: 'Collapse Filters',
    color: 'Color',
    filters: 'Background Filters',
    font: 'Font',
    right: 'Right',
    left: 'Left',
    bottom: 'Bottom',
    top: 'Top',
    bottomRight: 'Bottom Right',
    bottomLeft: 'Bottom Left',
    topRight: 'Top Right',
    topLeft: 'Top Left',
    center: 'Center',
    gradientDirection: 'Gradient Direction',
    gradientColor1: 'Color 1',
    gradientColor2: 'Color 2',
    gradientColor3: 'Color 3',
    gradientColor4: 'Color 4',
    gradientColor5: 'Color 5',
    gradientColor6: 'Color 6',
    customGradient: 'Custom Gradient',
    customGradientCSS: 'Custom CSS',
    gradientPreview: 'Preview',
    linear: 'Linear',
    radial: 'Radial',
    conic: 'Conic',
    toRight: 'To Right',
    toLeft: 'To Left',
    toBottom: 'To Bottom',
    toTop: 'To Top',
    toBottomRight: 'To Bottom Right',
    toBottomLeft: 'To Bottom Left',
    toTopRight: 'To Top Right',
    toTopLeft: 'To Top Left',
    circle: 'Circle',
    ellipse: 'Ellipse',
    fromCenter: 'From Center',
    
    // Image filters
    imageFilters: 'Image Filters',
    
    // Presets
    presets: 'Presets',
    createPreset: 'Create Preset',
    presetName: 'Preset Name',
    noPresets: 'No presets created. Create your first preset to quickly switch between settings.',
    renamePreset: 'Rename Preset',
    updatePreset: 'Update Preset with Current Settings',
    deletePreset: 'Delete Preset',
    createNewPreset: 'Create New Preset',
    presetDescription: 'Preset will save current color, font and background settings.',

    // Font categories
    fontCategories: {
      sansSerif: 'Sans Serif',
      serif: 'Serif',
      monospace: 'Monospace',
      display: 'Display',
      handwriting: 'Handwriting',
      pixel: 'Pixel',
      terminal: 'Terminal',
    },
    
    // Translations
    aiTranslationsDisclaimer: 'All translations are AI-generated',

    // Font names and categories
    fonts: {
      systemFont: 'System Font',
      systemFonts: 'System Fonts',
      serifFonts: 'Serif Fonts',
      monospaceFonts: 'Monospace Fonts',
      displayFonts: 'Display Fonts',
      pixelFonts: 'Pixel Fonts',
      terminalFonts: 'Terminal Fonts',
      modernFonts: 'Additional Modern Fonts',
      decorativeFonts: 'Additional Decorative Fonts',
    },

    // Reset and export
    resetAllColors: 'Reset All Colors to Accent',
    resetAllSettings: 'Reset All Settings',
  },

  // Lists
  lists: {
    newList: 'New List',
    listName: 'List Name',
    addLink: 'Add Link',
    linkName: 'Link Name',
    linkUrl: 'Link URL',
    editList: 'Edit List',
    deleteList: 'Delete List',
    listIcon: 'List Icon',
    listColor: 'List Color',
    linkColor: 'Link Color',
    hideIcons: 'Hide Link Icons',
    openInNewWindow: 'Open in New Window',
    copyLink: 'Copy Link',
    editLink: 'Edit Link',
    deleteLink: 'Delete Link',
    title: 'Title',
    url: 'URL',
    addNewList: 'Add List',
    iconColor: 'Icon Color',
  },

  // Quick links
  fastLinks: {
    newFastLink: 'New Quick Link',
    fastLinkName: 'Quick Link Name',
    fastLinkUrl: 'URL',
    editFastLink: 'Edit Quick Link',
    deleteFastLink: 'Delete Quick Link',
    fastLinkColor: 'Quick Link Color',
  },

  // Search
  search: {
    placeholder: 'Search...',
    searchWith: 'Search with',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Search fonts...',
    fontsNotFound: 'Fonts not found',
    searchInGoogle: 'Search in Google...',
    searchInYandex: 'Search in Yandex...',
    searchInBing: 'Search in Bing...',
    searchInDuckDuckGo: 'Search in DuckDuckGo...',
    searchInYahoo: 'Search in Yahoo...',
    searchInBaidu: 'Search in Baidu...',
    searchInStartpage: 'Search in Startpage...',
    searchInSearX: 'Search in SearX...',
    searchInEcosia: 'Search in Ecosia...',
    searchInBrave: 'Search in Brave...',
  },

  // Errors
  errors: {
    invalidUrl: 'Invalid URL',
    nameRequired: 'Name is required',
    urlRequired: 'URL is required',
    fileUploadError: 'File upload error',
    settingsImportError: 'Settings import error',
    settingsExportError: 'Settings export error',
    invalidImageUrl: 'Invalid image URL',
    imageValidationError: 'Image validation error',
    imageLoadFailed: 'Failed to load image',
    imageTooSmall: 'Image is too small',
    settingsImported: 'Settings imported successfully! The page will be reloaded to apply all changes.',
    settingsExported: 'Settings exported successfully',
    parseError: 'Parse error:',
    invalidFileFormat: 'Error reading settings file. Make sure the file has the correct format.',
    importError: 'Error importing settings. Check console for details.',
    exportError: 'Error exporting settings. Check console for details.',
    resetConfirm: 'Are you sure you want to reset all settings to default values? This action cannot be undone. All custom settings, including backgrounds, lists and quick links, will be completely removed from localStorage.',
    deleteListConfirm: 'Are you sure you want to delete this list? This action cannot be undone.',
    deleteLinkConfirm: 'Are you sure you want to delete this link?',
    loadingError: 'Loading error',
    backgroundLoadError: 'Failed to load background image',
    criticalError: 'A critical error occurred. The page will be reloaded.',
    jsError: 'JavaScript Error',
    promiseRejection: 'Unhandled Promise Error',
  },

  // Tooltips
  tooltips: {
    settings: 'Settings',
    addList: 'Add List',
    addFastLink: 'Add Quick Link',
    editItem: 'Edit',
    deleteItem: 'Delete',
    updateItem: 'Update',
    resetColor: 'Reset Color',
    openAllLinks: 'Open all links in new tabs',
    addLink: 'Add Link',
    openLink: 'Open Link',
    copyLink: 'Copy Link',
    dragToReorder: 'Drag to reorder',
    exportSettings: 'Export all settings to file',
    importSettings: 'Import settings from file',
    resetAllSettings: 'Reset All Settings',
    closeSettings: 'Close Settings',
    generateColor: 'Generate color based on image',
    applyAccentColor: 'Apply accent color to all elements',
    addRandomPhoto: 'Add random photo from internet',
    deleteImage: 'Delete Image',
    addColor: 'Add Color',
    deleteColor: 'Delete Color',
    resetFilters: 'Reset Filters',
    expandFilters: 'Expand Filters',
    collapseFilters: 'Collapse Filters',
    stopAutoSwitch: 'Stop automatic switching',
    startAutoSwitch: 'Start automatic switching',
  },

  // Time
  time: {
    seconds: 'sec',
    minutes: 'min',
    hours: 'h',
    days: 'days',
    months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
    weekdays: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  },

  // Units
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sec',
    minutes: 'min',
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
  },

  // Dialogs
  dialogs: {
    newName: 'New Name',
    newListName: 'New List Name',
    linkTitle: 'Link Title',
    linkColor: 'Link Color',
    separatorColor: 'Separator Color',
    titleColor: 'Title Color',
  },

  // ARIA labels
  ariaLabels: {
    settings: 'Settings',
    resetAllSettings: 'Reset All Settings',
    closeSettings: 'Close Settings',
    applyAccentColor: 'Apply accent color to all elements',
    addList: 'Add List',
    addRandomPhoto: 'Add random photo',
    deleteImage: 'Delete Image',
    addColor: 'Add Color',
    deleteColor: 'Delete Color',
    resetFilters: 'Reset Filters',
    expandFilters: 'Expand Filters',
    collapseFilters: 'Collapse Filters',
  },

  // Border radius
  radius: {
    none: 'None',
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
    full: 'Full',
  },

  // Font categories
  fonts: {
    system: 'System',
    serif: 'Serif',
    monospace: 'Monospace',
    display: 'Display',
    pixel: 'Pixel',
    terminal: 'Terminal',
    modern: 'Modern',
    decorative: 'Decorative',
  },
};
