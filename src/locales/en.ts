export const en = {
  // Common
  common: {
    apply: 'Apply',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    update: 'Update',
    reset: 'Reset',
    add: 'Add',
    remove: 'Remove',
    close: 'Close',
    open: 'Open',
    enable: 'Enable',
    disable: 'Disable',
    show: 'Show',
    hide: 'Hide',
    yes: 'Yes',
    no: 'No',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information',
  },

  // Settings
  settings: {
    title: 'Settings',
    basicSettings: 'Basic Settings',
    clockSettings: 'Clock Settings',
    searchSettings: 'Search Engine',
    fastLinksSettings: 'Quick Links',
    backgroundSettings: 'Background',
    presetsSettings: 'Presets',
    listsSettings: 'Lists Settings',
    
    // Basic settings
    language: 'Interface Language',
    exportSettings: 'Export',
    importSettings: 'Import',
    theme: 'Theme',
    accentColor: 'Accent Color',
    borderRadius: 'Border Radius',
    cleanMode: 'Clean Mode',
    cleanModeDescription: 'Hides list open buttons',
    colorScheme: 'Color Scheme',
    borderRounding: 'Element Rounding',
    
    // Clock
    showClock: 'Show Clock',
    showSeconds: 'Show Seconds',
    showDate: 'Show Date',
    clockSize: 'Clock Size',
    clockColor: 'Clock and Date Color',
    
    // Search
    showSearch: 'Show Search',
    searchEngine: 'Search Engine',
    searchSize: 'Search Size',
    searchBackgroundColor: 'Search Background Color',
    searchBorderColor: 'Search Border Color',
    searchTextColor: 'Search Text Color',
    searchBackdropBlur: 'Search Background Blur',
    showSearchEngine: 'Show Search Engine',
    backgroundBlur: 'Background Blur',
    
    // Fast links
    showFastLinks: 'Show Quick Links',
    fastLinksColumns: 'Number of Columns',
    fastLinksSize: 'Quick Links Size',
    fastLinksBackdropBlur: 'Quick Links Background Blur',
    addFastLink: 'Add Quick Link',
    textColor: 'Title Text Color',
    backdropColor: 'Backdrop Color',
    iconBackgroundColor: 'Icon Background Color',
    display: 'Display',
    hideIcons: 'Hide Icons',
    hideText: 'Hide Text',
    backgroundEffects: 'Icon Background Effects',

    // Lists
    showLists: 'Show Link Lists',
    listsColumns: 'Number of Columns',
    listsBackgroundColor: 'Lists Background Color',
    listsBackdropBlur: 'Lists Background Blur',
    addNewList: 'Add New List',
    listBackground: 'List Background',
    backgroundColor: 'Background Color',
    borderColor: 'Border Color',
    borderThickness: 'Border Thickness',
    hideBorder: 'Hide Border',
    hideBackground: 'Hide Background',
    separator: 'Separator',
    separatorColor: 'Separator Color',
    separatorThickness: 'Separator Thickness',
    hideSeparator: 'Hide Separator',
    colorsAndIcons: 'Colors and Icons',
    titleColor: 'Title Color',
    linkColor: 'Link Color',
    hideLinkIcons: 'Hide Link Icons',
    listGrid: 'List Grid',
    columnsCount: 'Number of Columns',
    listManagement: 'List Management',
    
    // Background
    backgroundType: 'Background Type',
    solidColor: 'Solid Color',
    gradient: 'Gradient',
    image: 'Image',
    brightness: 'Brightness',
    contrast: 'Contrast',
    saturation: 'Saturation',
    blur: 'Blur',
    hueRotate: 'Hue Rotate',
    sepia: 'Sepia',
    grayscale: 'Grayscale',
    invert: 'Invert',
    shadowOverlay: 'Shadow Overlay',
    parallaxEffect: 'Parallax Effect',
    autoSwitch: 'Auto Switch',
    switchInterval: 'Switch Interval',
    addImages: 'Add Images',
    uploadImages: 'Upload Images',
    addImage: 'Add Image',
    checking: 'Checking...',
    shadowBottom: 'Bottom Shadow',
    shadowDescription: 'Gradient shadow for better list visibility',
    intensity: 'Intensity:',
    height: 'Height:',
    gallery: 'Gallery',
    onLoad: 'On Load',
    daily: 'Daily',
    gradientType: 'Gradient Type',
    linear: 'Linear',
    radial: 'Radial',
    colors: 'Colors',
    direction: 'Direction',
    position: 'Position',
    customCSS: 'Custom CSS Gradient (optional)',
    customCSSDescription: 'Enter CSS gradient string to apply instead of manual settings',
    filters: 'Background Filters',
    font: 'Font',
    right: 'Right',
    left: 'Left',
    bottom: 'Bottom',
    top: 'Top',
    bottomRight: 'Bottom Right',
    bottomLeft: 'Bottom Left',
    topRight: 'Top Right',
    topLeft: 'Top Left',
    center: 'Center',
    
    // Presets
    presets: 'Presets',
    createPreset: 'Create Preset',
    presetName: 'Preset Name',
    noPresets: 'No presets created. Create your first preset for quick switching between settings.',
    renamePreset: 'Rename Preset',
    updatePreset: 'Update Preset with Current Settings',
    deletePreset: 'Delete Preset',
    
    // Reset and export
    resetAllColors: 'Reset All Colors to Accent',
    resetAllSettings: 'Reset All Settings',
  },

  // Lists
  lists: {
    newList: 'New List',
    listName: 'List Name',
    addLink: 'Add Link',
    linkName: 'Link Name',
    linkUrl: 'Link URL',
    editList: 'Edit List',
    deleteList: 'Delete List',
    listIcon: 'List Icon',
    listColor: 'List Color',
    hideIcons: 'Hide Link Icons',
    openInNewWindow: 'Open in New Window',
    copyLink: 'Copy Link',
    editLink: 'Edit Link',
    deleteLink: 'Delete Link',
    title: 'Title',
    url: 'URL',
  },

  // Dialogs and forms
  dialogs: {
    newName: 'New Name',
    newListName: 'New List Name',
    linkTitle: 'Link Title',
    linkColor: 'Link Color',
    separatorColor: 'Separator Color',
    titleColor: 'Title Color',
  },

  // Fast links
  fastLinks: {
    newFastLink: 'New Quick Link',
    fastLinkName: 'Name',
    fastLinkUrl: 'URL',
    editFastLink: 'Edit Quick Link',
    deleteFastLink: 'Delete Quick Link',
    fastLinkColor: 'Quick Link Color',
  },

  // Search
  search: {
    placeholder: 'Search...',
    searchWith: 'Search with',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Search fonts...',
  },

  // Errors and notifications
  errors: {
    invalidUrl: 'Invalid URL',
    nameRequired: 'Name is required',
    urlRequired: 'URL is required',
    fileUploadError: 'File upload error',
    settingsImportError: 'Settings import error',
    settingsExportError: 'Settings export error',
    invalidImageUrl: 'Invalid image URL',
    imageValidationError: 'Error validating image',
    settingsImported: 'Settings imported successfully! The page will reload to apply all changes.',
    settingsExported: 'Settings exported successfully',
    parseError: 'Error parsing file:',
    invalidFileFormat: 'Error reading settings file. Make sure the file has the correct format.',
    importError: 'Error importing settings. Check console for details.',
    exportError: 'Error exporting settings. Check console for details.',
    resetConfirm: 'Are you sure you want to reset all settings to default values? This action cannot be undone. All custom settings, including backgrounds, lists and quick links, will be completely removed from localStorage.',
  },

  // Tooltips
  tooltips: {
    settings: 'Settings',
    addList: 'Add List',
    addFastLink: 'Add Quick Link',
    editItem: 'Edit',
    deleteItem: 'Delete',
    updateItem: 'Update',
    resetColor: 'Reset Color',
    openLink: 'Open Link',
    copyLink: 'Copy Link',
    dragToReorder: 'Drag to reorder',
    exportSettings: 'Export all settings to file',
    importSettings: 'Import settings from file',
    resetAllSettings: 'Reset all settings',
    closeSettings: 'Close settings',
    generateColor: 'Generate color based on image',
    applyAccentColor: 'Apply accent color to all elements',
    addRandomPhoto: 'Add random photo from internet',
    deleteImage: 'Delete image',
    addColor: 'Add color',
    deleteColor: 'Delete color',
    resetFilters: 'Reset filters',
    expandFilters: 'Expand filters',
    collapseFilters: 'Collapse filters',
    stopAutoSwitch: 'Stop auto switch',
    startAutoSwitch: 'Start auto switch',
  },

  // Aria-labels
  ariaLabels: {
    settings: 'Settings',
    resetAllSettings: 'Reset all settings',
    closeSettings: 'Close settings',
    applyAccentColor: 'Apply accent color to all elements',
    addList: 'Add list',
    addRandomPhoto: 'Add random photo',
    deleteImage: 'Delete image',
    addColor: 'Add color',
    deleteColor: 'Delete color',
    resetFilters: 'Reset filters',
    expandFilters: 'Expand filters',
    collapseFilters: 'Collapse filters',
  },

  // Border radius
  radius: {
    none: 'None',
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
    full: 'Full',
  },

  // Time and date
  time: {
    seconds: 'sec',
    minutes: 'min',
    hours: 'h',
    days: 'd',
    months: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
    weekdays: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
  },

  // Units and sizes
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sec',
    minutes: 'min',
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
  },
};
