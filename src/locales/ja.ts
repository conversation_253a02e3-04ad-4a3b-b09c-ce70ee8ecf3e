export const ja = {
  // 共通
  common: {
    apply: '適用',
    cancel: 'キャンセル',
    save: '保存',
    delete: '削除',
    edit: '編集',
    update: '更新',
    reset: 'リセット',
    add: '追加',
    remove: '削除',
    close: '閉じる',
    open: '開く',
    enable: '有効',
    disable: '無効',
    show: '表示',
    hide: '非表示',
    yes: 'はい',
    no: 'いいえ',
    loading: '読み込み中...',
    error: 'エラー',
    success: '成功',
    warning: '警告',
    info: '情報',
  },

  // 設定
  settings: {
    title: '設定',
    basicSettings: '基本設定',
    clockSettings: '時計設定',
    searchSettings: '検索エンジン',
    fastLinksSettings: 'クイックリンク',
    backgroundSettings: '背景',
    presetsSettings: 'プリセット',
    listsSettings: 'リスト設定',
    
    // 基本設定
    language: 'インターフェース言語',
    exportSettings: 'エクスポート',
    importSettings: 'インポート',
    theme: 'テーマ',
    accentColor: 'アクセントカラー',
    borderRadius: '角の丸み',
    cleanMode: 'クリーンモード',
    cleanModeDescription: 'リスト開くボタンを非表示',
    
    // 時計
    showClock: '時計を表示',
    showSeconds: '秒を表示',
    showDate: '日付を表示',
    clockSize: '時計サイズ',
    clockColor: '時計と日付の色',
    
    // 検索
    showSearch: '検索を表示',
    searchEngine: '検索エンジン',
    searchSize: '検索サイズ',
    searchBackgroundColor: '検索背景色',
    searchBorderColor: '検索枠線色',
    searchTextColor: '検索テキスト色',
    searchBackdropBlur: '検索背景ぼかし',
    
    // クイックリンク
    showFastLinks: 'クイックリンクを表示',
    fastLinksColumns: '列数',
    fastLinksSize: 'クイックリンクサイズ',
    fastLinksBackdropBlur: 'クイックリンク背景ぼかし',
    addFastLink: 'クイックリンクを追加',
    
    // リスト
    showLists: 'リンクリストを表示',
    listsColumns: '列数',
    listsBackgroundColor: 'リスト背景色',
    listsBackdropBlur: 'リスト背景ぼかし',
    addNewList: '新しいリストを追加',
    
    // 背景
    backgroundType: '背景タイプ',
    solidColor: '単色',
    gradient: 'グラデーション',
    image: '画像',
    brightness: '明度',
    contrast: 'コントラスト',
    saturation: '彩度',
    blur: 'ぼかし',
    hueRotate: '色相回転',
    sepia: 'セピア',
    grayscale: 'グレースケール',
    invert: '反転',
    shadowOverlay: 'シャドウオーバーレイ',
    parallaxEffect: 'パララックス効果',
    autoSwitch: '自動切り替え',
    switchInterval: '切り替え間隔',
    addImages: '画像を追加',
    uploadImages: '画像をアップロード',
    
    // プリセット
    presets: 'プリセット',
    createPreset: 'プリセットを作成',
    presetName: 'プリセット名',
    noPresets: 'プリセットが作成されていません。設定を素早く切り替えるために最初のプリセットを作成してください。',
    renamePreset: 'プリセット名を変更',
    updatePreset: '現在の設定でプリセットを更新',
    deletePreset: 'プリセットを削除',
    
    // リセットとエクスポート
    resetAllColors: 'すべての色をアクセントにリセット',
    resetAllSettings: 'すべての設定をリセット',
  },

  // リスト
  lists: {
    newList: '新しいリスト',
    listName: 'リスト名',
    addLink: 'リンクを追加',
    linkName: 'リンク名',
    linkUrl: 'リンクURL',
    editList: 'リストを編集',
    deleteList: 'リストを削除',
    listIcon: 'リストアイコン',
    listColor: 'リスト色',
    hideIcons: 'リンクアイコンを非表示',
    openInNewWindow: '新しいウィンドウで開く',
    copyLink: 'リンクをコピー',
    editLink: 'リンクを編集',
    deleteLink: 'リンクを削除',
  },

  // クイックリンク
  fastLinks: {
    newFastLink: '新しいクイックリンク',
    fastLinkName: '名前',
    fastLinkUrl: 'URL',
    editFastLink: 'クイックリンクを編集',
    deleteFastLink: 'クイックリンクを削除',
    fastLinkColor: 'クイックリンク色',
  },

  // 検索
  search: {
    placeholder: '検索...',
    searchWith: '検索エンジン',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
  },

  // エラーと通知
  errors: {
    invalidUrl: '無効なURL',
    nameRequired: '名前が必要です',
    urlRequired: 'URLが必要です',
    fileUploadError: 'ファイルアップロードエラー',
    settingsImportError: '設定インポートエラー',
    settingsExportError: '設定エクスポートエラー',
  },

  // ツールチップ
  tooltips: {
    settings: '設定',
    addList: 'リストを追加',
    addFastLink: 'クイックリンクを追加',
    editItem: '編集',
    deleteItem: '削除',
    updateItem: '更新',
    resetColor: '色をリセット',
    openLink: 'リンクを開く',
    copyLink: 'リンクをコピー',
    dragToReorder: 'ドラッグして並び替え',
    exportSettings: 'すべての設定をファイルにエクスポート',
    importSettings: 'ファイルから設定をインポート',
  },

  // 時間と日付
  time: {
    seconds: '秒',
    minutes: '分',
    hours: '時間',
    days: '日',
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    weekdays: ['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日'],
  },

  // 単位とサイズ
  units: {
    pixels: 'px',
    percent: '%',
    seconds: '秒',
    minutes: '分',
    small: '小',
    medium: '中',
    large: '大',
  },
};
