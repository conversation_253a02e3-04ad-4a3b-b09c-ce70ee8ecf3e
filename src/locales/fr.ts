export const fr = {
  // Commun
  common: {
    apply: 'Appliquer',
    cancel: 'Annuler',
    save: 'Enregistre<PERSON>',
    delete: 'Supprimer',
    edit: 'Modifier',
    update: 'Mettre à jour',
    reset: 'Réinitialiser',
    add: 'Ajouter',
    remove: 'Supprimer',
    close: 'Fermer',
    open: 'Ouvrir',
    enable: 'Activer',
    disable: 'Désactiver',
    show: 'Afficher',
    hide: 'Masquer',
    yes: 'Oui',
    no: 'Non',
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    warning: 'Avertissement',
    info: 'Information',
  },

  // Paramètres
  settings: {
    title: 'Paramètres',
    basicSettings: 'Paramètres de base',
    clockSettings: 'Paramètres de l\'horloge',
    searchSettings: 'Moteur de recherche',
    fastLinksSettings: 'Liens rapides',
    backgroundSettings: 'Arrière-plan',
    presetsSettings: 'Préréglages',
    listsSettings: 'Paramètres des listes',
    
    // Paramètres de base
    language: 'Langue de l\'interface',
    exportSettings: 'Exporter',
    importSettings: 'Importer',
    theme: 'Thème',
    accentColor: 'Couleur d\'accent',
    borderRadius: 'Rayon des bordures',
    cleanMode: 'Mode propre',
    cleanModeDescription: 'Masque les boutons d\'ouverture des listes',
    
    // Horloge
    showClock: 'Afficher l\'horloge',
    showSeconds: 'Afficher les secondes',
    showDate: 'Afficher la date',
    clockSize: 'Taille de l\'horloge',
    clockColor: 'Couleur de l\'horloge et de la date',
    
    // Recherche
    showSearch: 'Afficher la recherche',
    searchEngine: 'Moteur de recherche',
    searchSize: 'Taille de la recherche',
    searchBackgroundColor: 'Couleur d\'arrière-plan de la recherche',
    searchBorderColor: 'Couleur de bordure de la recherche',
    searchTextColor: 'Couleur du texte de recherche',
    searchBackdropBlur: 'Flou d\'arrière-plan de la recherche',
    
    // Liens rapides
    showFastLinks: 'Afficher les liens rapides',
    fastLinksColumns: 'Nombre de colonnes',
    fastLinksSize: 'Taille des liens rapides',
    fastLinksBackdropBlur: 'Flou d\'arrière-plan des liens rapides',
    addFastLink: 'Ajouter un lien rapide',
    
    // Listes
    showLists: 'Afficher les listes de liens',
    listsColumns: 'Nombre de colonnes',
    listsBackgroundColor: 'Couleur d\'arrière-plan des listes',
    listsBackdropBlur: 'Flou d\'arrière-plan des listes',
    addNewList: 'Ajouter une nouvelle liste',
    
    // Arrière-plan
    backgroundType: 'Type d\'arrière-plan',
    solidColor: 'Couleur unie',
    gradient: 'Dégradé',
    image: 'Image',
    brightness: 'Luminosité',
    contrast: 'Contraste',
    saturation: 'Saturation',
    blur: 'Flou',
    hueRotate: 'Rotation de teinte',
    sepia: 'Sépia',
    grayscale: 'Niveaux de gris',
    invert: 'Inverser',
    shadowOverlay: 'Superposition d\'ombre',
    parallaxEffect: 'Effet parallaxe',
    autoSwitch: 'Changement automatique',
    switchInterval: 'Intervalle de changement',
    addImages: 'Ajouter des images',
    uploadImages: 'Télécharger des images',
    
    // Préréglages
    presets: 'Préréglages',
    createPreset: 'Créer un préréglage',
    presetName: 'Nom du préréglage',
    noPresets: 'Aucun préréglage créé. Créez votre premier préréglage pour basculer rapidement entre les paramètres.',
    renamePreset: 'Renommer le préréglage',
    updatePreset: 'Mettre à jour le préréglage avec les paramètres actuels',
    deletePreset: 'Supprimer le préréglage',
    
    // Traductions
    aiTranslationsDisclaimer: 'Toutes les traductions sont générées par IA',

    // Réinitialisation et export
    resetAllColors: 'Réinitialiser toutes les couleurs à l\'accent',
    resetAllSettings: 'Réinitialiser tous les paramètres',
  },

  // Listes
  lists: {
    newList: 'Nouvelle liste',
    listName: 'Nom de la liste',
    addLink: 'Ajouter un lien',
    linkName: 'Nom du lien',
    linkUrl: 'URL du lien',
    editList: 'Modifier la liste',
    deleteList: 'Supprimer la liste',
    listIcon: 'Icône de la liste',
    listColor: 'Couleur de la liste',
    linkColor: 'Couleur du lien',
    hideIcons: 'Masquer les icônes des liens',
    openInNewWindow: 'Ouvrir dans une nouvelle fenêtre',
    copyLink: 'Copier le lien',
    editLink: 'Modifier le lien',
    deleteLink: 'Supprimer le lien',
  },

  // Liens rapides
  fastLinks: {
    newFastLink: 'Nouveau lien rapide',
    fastLinkName: 'Nom',
    fastLinkUrl: 'URL',
    editFastLink: 'Modifier le lien rapide',
    deleteFastLink: 'Supprimer le lien rapide',
    fastLinkColor: 'Couleur du lien rapide',
  },

  // Recherche
  search: {
    placeholder: 'Rechercher...',
    searchWith: 'Rechercher avec',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
  },

  // Erreurs et notifications
  errors: {
    invalidUrl: 'URL invalide',
    nameRequired: 'Le nom est requis',
    urlRequired: 'L\'URL est requise',
    fileUploadError: 'Erreur de téléchargement de fichier',
    settingsImportError: 'Erreur d\'importation des paramètres',
    settingsExportError: 'Erreur d\'exportation des paramètres',
    criticalError: 'Une erreur critique s\'est produite. La page va être rechargée.',
    jsError: 'Erreur JavaScript',
    promiseRejection: 'Erreur Promise non gérée',
  },

  // Info-bulles
  tooltips: {
    settings: 'Paramètres',
    addList: 'Ajouter une liste',
    addFastLink: 'Ajouter un lien rapide',
    editItem: 'Modifier',
    deleteItem: 'Supprimer',
    updateItem: 'Mettre à jour',
    resetColor: 'Réinitialiser la couleur',
    openLink: 'Ouvrir le lien',
    copyLink: 'Copier le lien',
    dragToReorder: 'Glisser pour réorganiser',
    exportSettings: 'Exporter tous les paramètres vers un fichier',
    importSettings: 'Importer les paramètres depuis un fichier',
  },

  // Temps et date
  time: {
    seconds: 'sec',
    minutes: 'min',
    hours: 'h',
    days: 'j',
    months: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
    weekdays: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
  },

  // Unités et tailles
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sec',
    minutes: 'min',
    small: 'Petit',
    medium: 'Moyen',
    large: 'Grand',
  },
};
