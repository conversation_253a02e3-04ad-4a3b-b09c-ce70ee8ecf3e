export const it = {
  // Comune
  common: {
    apply: 'Applica',
    cancel: '<PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON>',
    delete: '<PERSON><PERSON>',
    edit: 'Modi<PERSON>',
    update: 'Aggiorna',
    reset: 'R<PERSON><PERSON><PERSON>',
    add: 'Aggiungi',
    remove: '<PERSON><PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON>',
    open: '<PERSON><PERSON>',
    enable: '<PERSON><PERSON><PERSON>',
    disable: '<PERSON><PERSON><PERSON><PERSON>',
    show: '<PERSON><PERSON>',
    hide: 'Nascondi',
    yes: 'S<PERSON>',
    no: 'No',
    loading: 'Caricamento...',
    error: 'Errore',
    success: 'Successo',
    warning: 'Avviso',
    info: 'Informazione',
  },

  // Impostazioni
  settings: {
    title: 'Impostazioni',
    basicSettings: 'Impostazioni di base',
    clockSettings: 'Impostazioni orologio',
    searchSettings: 'Motore di ricerca',
    fastLinksSettings: 'Collegamenti rapidi',
    backgroundSettings: 'Sfondo',
    presetsSettings: 'Preimpostazioni',
    listsSettings: 'Impostazioni elenchi',
    
    // Impostazioni di base
    language: 'Lingua dell\'interfaccia',
    exportSettings: 'Esporta',
    importSettings: 'Importa',
    theme: '<PERSON><PERSON>',
    accentColor: 'Colore accento',
    borderRadius: 'Raggio bordo',
    cleanMode: 'Modalità pulita',
    cleanModeDescription: 'Nasconde i pulsanti di apertura degli elenchi',
    
    // Orologio
    showClock: 'Mostra orologio',
    showSeconds: 'Mostra secondi',
    showDate: 'Mostra data',
    clockSize: 'Dimensione orologio',
    clockColor: 'Colore orologio e data',
    
    // Ricerca
    showSearch: 'Mostra ricerca',
    searchEngine: 'Motore di ricerca',
    searchSize: 'Dimensione ricerca',
    searchBackgroundColor: 'Colore sfondo ricerca',
    searchBorderColor: 'Colore bordo ricerca',
    searchTextColor: 'Colore testo ricerca',
    searchBackdropBlur: 'Sfocatura sfondo ricerca',
    
    // Collegamenti rapidi
    showFastLinks: 'Mostra collegamenti rapidi',
    fastLinksColumns: 'Numero di colonne',
    fastLinksSize: 'Dimensione collegamenti rapidi',
    fastLinksBackdropBlur: 'Sfocatura sfondo collegamenti rapidi',
    addFastLink: 'Aggiungi collegamento rapido',
    
    // Elenchi
    showLists: 'Mostra elenchi collegamenti',
    listsColumns: 'Numero di colonne',
    listsBackgroundColor: 'Colore sfondo elenchi',
    listsBackdropBlur: 'Sfocatura sfondo elenchi',
    addNewList: 'Aggiungi nuovo elenco',
    
    // Sfondo
    backgroundType: 'Tipo di sfondo',
    solidColor: 'Colore solido',
    gradient: 'Gradiente',
    image: 'Immagine',
    brightness: 'Luminosità',
    contrast: 'Contrasto',
    saturation: 'Saturazione',
    blur: 'Sfocatura',
    hueRotate: 'Rotazione tonalità',
    sepia: 'Seppia',
    grayscale: 'Scala di grigi',
    invert: 'Inverti',
    shadowOverlay: 'Sovrapposizione ombra',
    parallaxEffect: 'Effetto parallasse',
    autoSwitch: 'Cambio automatico',
    switchInterval: 'Intervallo di cambio',
    addImages: 'Aggiungi immagini',
    uploadImages: 'Carica immagini',
    
    // Preimpostazioni
    presets: 'Preimpostazioni',
    createPreset: 'Crea preimpostazione',
    presetName: 'Nome preimpostazione',
    noPresets: 'Nessuna preimpostazione creata. Crea la tua prima preimpostazione per cambiare rapidamente tra le impostazioni.',
    renamePreset: 'Rinomina preimpostazione',
    updatePreset: 'Aggiorna preimpostazione con impostazioni correnti',
    deletePreset: 'Elimina preimpostazione',
    
    // Traduzioni
    aiTranslationsDisclaimer: 'Tutte le traduzioni sono generate dall\'IA',

    // Ripristina ed esporta
    resetAllColors: 'Ripristina tutti i colori all\'accento',
    resetAllSettings: 'Ripristina tutte le impostazioni',
  },

  // Elenchi
  lists: {
    newList: 'Nuovo elenco',
    listName: 'Nome elenco',
    addLink: 'Aggiungi collegamento',
    linkName: 'Nome collegamento',
    linkUrl: 'URL collegamento',
    editList: 'Modifica elenco',
    deleteList: 'Elimina elenco',
    listIcon: 'Icona elenco',
    listColor: 'Colore elenco',
    linkColor: 'Colore collegamento',
    hideIcons: 'Nascondi icone collegamenti',
    openInNewWindow: 'Apri in nuova finestra',
    copyLink: 'Copia collegamento',
    editLink: 'Modifica collegamento',
    deleteLink: 'Elimina collegamento',
  },

  // Collegamenti rapidi
  fastLinks: {
    newFastLink: 'Nuovo collegamento rapido',
    fastLinkName: 'Nome',
    fastLinkUrl: 'URL',
    editFastLink: 'Modifica collegamento rapido',
    deleteFastLink: 'Elimina collegamento rapido',
    fastLinkColor: 'Colore collegamento rapido',
  },

  // Ricerca
  search: {
    placeholder: 'Cerca...',
    searchWith: 'Cerca con',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Cerca font...',
    fontsNotFound: 'Font non trovati',
    searchInGoogle: 'Cerca in Google...',
    searchInYandex: 'Cerca in Yandex...',
    searchInBing: 'Cerca in Bing...',
    searchInDuckDuckGo: 'Cerca in DuckDuckGo...',
    searchInYahoo: 'Cerca in Yahoo...',
    searchInBaidu: 'Cerca in Baidu...',
    searchInStartpage: 'Cerca in Startpage...',
    searchInSearX: 'Cerca in SearX...',
    searchInEcosia: 'Cerca in Ecosia...',
    searchInBrave: 'Cerca in Brave...',
  },

  // Errori e notifiche
  errors: {
    invalidUrl: 'URL non valido',
    nameRequired: 'Il nome è obbligatorio',
    urlRequired: 'L\'URL è obbligatorio',
    fileUploadError: 'Errore caricamento file',
    settingsImportError: 'Errore importazione impostazioni',
    settingsExportError: 'Errore esportazione impostazioni',
    criticalError: 'Si è verificato un errore critico. La pagina verrà ricaricata.',
    jsError: 'Errore JavaScript',
    promiseRejection: 'Errore Promise non gestito',
  },

  // Tooltip
  tooltips: {
    settings: 'Impostazioni',
    addList: 'Aggiungi elenco',
    addFastLink: 'Aggiungi collegamento rapido',
    editItem: 'Modifica',
    deleteItem: 'Elimina',
    updateItem: 'Aggiorna',
    resetColor: 'Ripristina colore',
    openLink: 'Apri collegamento',
    copyLink: 'Copia collegamento',
    dragToReorder: 'Trascina per riordinare',
    exportSettings: 'Esporta tutte le impostazioni in file',
    importSettings: 'Importa impostazioni da file',
  },

  // Tempo e data
  time: {
    seconds: 'sec',
    minutes: 'min',
    hours: 'h',
    days: 'g',
    months: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
    weekdays: ['Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato'],
  },

  // Unità e dimensioni
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sec',
    minutes: 'min',
    small: 'Piccolo',
    medium: 'Medio',
    large: 'Grande',
  },
};
