export const es = {
  // Común
  common: {
    apply: 'Aplicar',
    cancel: 'Cancelar',
    save: 'Guardar',
    delete: 'Eliminar',
    edit: 'Editar',
    update: 'Actualizar',
    reset: 'Restablecer',
    add: 'Añadir',
    remove: 'Quitar',
    close: 'Cerrar',
    open: 'Abrir',
    enable: 'Activar',
    disable: 'Desactivar',
    show: 'Mostrar',
    hide: 'Ocultar',
    yes: 'Sí',
    no: 'No',
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    warning: 'Advertencia',
    info: 'Información',
  },

  // Configuración
  settings: {
    title: 'Configuración',
    basicSettings: 'Configuración básica',
    clockSettings: 'Configuración del reloj',
    searchSettings: 'Motor de búsqueda',
    fastLinksSettings: 'Enlaces rápidos',
    backgroundSettings: 'Fondo',
    presetsSettings: 'Preajustes',
    listsSettings: 'Configuración de listas',
    
    // Configuración básica
    language: 'Idioma de la interfaz',
    exportSettings: 'Exportar',
    importSettings: 'Importar',
    theme: 'Tema',
    accentColor: 'Color de acento',
    borderRadius: 'Radio del borde',
    cleanMode: 'Modo limpio',
    cleanModeDescription: 'Oculta los botones de apertura de listas',
    
    // Reloj
    showClock: 'Mostrar reloj',
    showSeconds: 'Mostrar segundos',
    showDate: 'Mostrar fecha',
    clockSize: 'Tamaño del reloj',
    clockColor: 'Color del reloj y fecha',
    
    // Búsqueda
    showSearch: 'Mostrar búsqueda',
    searchEngine: 'Motor de búsqueda',
    searchSize: 'Tamaño de búsqueda',
    searchBackgroundColor: 'Color de fondo de búsqueda',
    searchBorderColor: 'Color del borde de búsqueda',
    searchTextColor: 'Color del texto de búsqueda',
    searchBackdropBlur: 'Desenfoque de fondo de búsqueda',
    
    // Enlaces rápidos
    showFastLinks: 'Mostrar enlaces rápidos',
    fastLinksColumns: 'Número de columnas',
    fastLinksSize: 'Tamaño de enlaces rápidos',
    fastLinksBackdropBlur: 'Desenfoque de fondo de enlaces rápidos',
    addFastLink: 'Añadir enlace rápido',
    
    // Listas
    showLists: 'Mostrar listas de enlaces',
    listsColumns: 'Número de columnas',
    listsBackgroundColor: 'Color de fondo de listas',
    listsBackdropBlur: 'Desenfoque de fondo de listas',
    addNewList: 'Añadir nueva lista',
    
    // Fondo
    backgroundType: 'Tipo de fondo',
    solidColor: 'Color sólido',
    gradient: 'Degradado',
    image: 'Imagen',
    brightness: 'Brillo',
    contrast: 'Contraste',
    saturation: 'Saturación',
    blur: 'Desenfoque',
    hueRotate: 'Rotación de matiz',
    sepia: 'Sepia',
    grayscale: 'Escala de grises',
    invert: 'Invertir',
    shadowOverlay: 'Superposición de sombra',
    parallaxEffect: 'Efecto parallax',
    autoSwitch: 'Cambio automático',
    switchInterval: 'Intervalo de cambio',
    addImages: 'Añadir imágenes',
    uploadImages: 'Subir imágenes',
    
    // Preajustes
    presets: 'Preajustes',
    createPreset: 'Crear preajuste',
    presetName: 'Nombre del preajuste',
    noPresets: 'No se han creado preajustes. Crea tu primer preajuste para cambiar rápidamente entre configuraciones.',
    renamePreset: 'Renombrar preajuste',
    updatePreset: 'Actualizar preajuste con configuración actual',
    deletePreset: 'Eliminar preajuste',
    
    // Traducciones
    aiTranslationsDisclaimer: 'Todas las traducciones son generadas por IA',

    // Restablecer y exportar
    resetAllColors: 'Restablecer todos los colores al acento',
    resetAllSettings: 'Restablecer toda la configuración',
  },

  // Listas
  lists: {
    newList: 'Nueva lista',
    listName: 'Nombre de la lista',
    addLink: 'Añadir enlace',
    linkName: 'Nombre del enlace',
    linkUrl: 'URL del enlace',
    editList: 'Editar lista',
    deleteList: 'Eliminar lista',
    listIcon: 'Icono de la lista',
    listColor: 'Color de la lista',
    linkColor: 'Color del enlace',
    hideIcons: 'Ocultar iconos de enlaces',
    openInNewWindow: 'Abrir en nueva ventana',
    copyLink: 'Copiar enlace',
    editLink: 'Editar enlace',
    deleteLink: 'Eliminar enlace',
  },

  // Enlaces rápidos
  fastLinks: {
    newFastLink: 'Nuevo enlace rápido',
    fastLinkName: 'Nombre',
    fastLinkUrl: 'URL',
    editFastLink: 'Editar enlace rápido',
    deleteFastLink: 'Eliminar enlace rápido',
    fastLinkColor: 'Color del enlace rápido',
  },

  // Búsqueda
  search: {
    placeholder: 'Buscar...',
    searchWith: 'Buscar con',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Buscar fuentes...',
    fontsNotFound: 'Fuentes no encontradas',
    searchInGoogle: 'Buscar en Google...',
    searchInYandex: 'Buscar en Yandex...',
    searchInBing: 'Buscar en Bing...',
    searchInDuckDuckGo: 'Buscar en DuckDuckGo...',
    searchInYahoo: 'Buscar en Yahoo...',
    searchInBaidu: 'Buscar en Baidu...',
    searchInStartpage: 'Buscar en Startpage...',
    searchInSearX: 'Buscar en SearX...',
    searchInEcosia: 'Buscar en Ecosia...',
    searchInBrave: 'Buscar en Brave...',
  },

  // Errores y notificaciones
  errors: {
    invalidUrl: 'URL inválida',
    nameRequired: 'El nombre es obligatorio',
    urlRequired: 'La URL es obligatoria',
    fileUploadError: 'Error al subir archivo',
    settingsImportError: 'Error al importar configuración',
    settingsExportError: 'Error al exportar configuración',
    criticalError: 'Se ha producido un error crítico. La página se recargará.',
    jsError: 'Error de JavaScript',
    promiseRejection: 'Error de Promise no manejado',
  },

  // Tooltips
  tooltips: {
    settings: 'Configuración',
    addList: 'Añadir lista',
    addFastLink: 'Añadir enlace rápido',
    editItem: 'Editar',
    deleteItem: 'Eliminar',
    updateItem: 'Actualizar',
    resetColor: 'Restablecer color',
    openLink: 'Abrir enlace',
    copyLink: 'Copiar enlace',
    dragToReorder: 'Arrastrar para reordenar',
    exportSettings: 'Exportar toda la configuración a archivo',
    importSettings: 'Importar configuración desde archivo',
  },

  // Tiempo y fecha
  time: {
    seconds: 'seg',
    minutes: 'min',
    hours: 'h',
    days: 'd',
    months: ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'],
    weekdays: ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'],
  },

  // Unidades y tamaños
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'seg',
    minutes: 'min',
    small: 'Pequeño',
    medium: 'Mediano',
    large: 'Grande',
  },
};
