export const pt = {
  // Comum
  common: {
    apply: 'Aplicar',
    cancel: 'Cancelar',
    save: '<PERSON><PERSON>',
    delete: 'Excluir',
    edit: 'Editar',
    update: 'Atualizar',
    reset: 'Redefinir',
    add: 'Adicionar',
    remove: 'Remover',
    close: '<PERSON><PERSON><PERSON>',
    open: 'Abrir',
    enable: 'Ativar',
    disable: 'Desati<PERSON>',
    show: 'Mostrar',
    hide: 'Ocultar',
    yes: 'Sim',
    no: 'Não',
    loading: 'Carregando...',
    error: 'Erro',
    success: 'Sucesso',
    warning: 'Aviso',
    info: 'Informação',
  },

  // Configurações
  settings: {
    title: 'Configurações',
    basicSettings: 'Configurações básicas',
    clockSettings: 'Configurações do relógio',
    searchSettings: 'Motor de busca',
    fastLinksSettings: 'Links rápidos',
    backgroundSettings: 'Plano de fundo',
    presetsSettings: 'Predefinições',
    listsSettings: 'Configurações das listas',
    
    // Configurações básicas
    language: 'Idioma da interface',
    exportSettings: 'Exportar',
    importSettings: 'Importar',
    theme: 'Tema',
    accentColor: 'Cor de destaque',
    borderRadius: 'Raio da borda',
    cleanMode: 'Modo limpo',
    cleanModeDescription: 'Oculta os botões de abertura das listas',
    
    // Relógio
    showClock: 'Mostrar relógio',
    showSeconds: 'Mostrar segundos',
    showDate: 'Mostrar data',
    clockSize: 'Tamanho do relógio',
    clockColor: 'Cor do relógio e data',
    
    // Busca
    showSearch: 'Mostrar busca',
    searchEngine: 'Motor de busca',
    searchSize: 'Tamanho da busca',
    searchBackgroundColor: 'Cor de fundo da busca',
    searchBorderColor: 'Cor da borda da busca',
    searchTextColor: 'Cor do texto da busca',
    searchBackdropBlur: 'Desfoque de fundo da busca',
    
    // Links rápidos
    showFastLinks: 'Mostrar links rápidos',
    fastLinksColumns: 'Número de colunas',
    fastLinksSize: 'Tamanho dos links rápidos',
    fastLinksBackdropBlur: 'Desfoque de fundo dos links rápidos',
    addFastLink: 'Adicionar link rápido',
    
    // Listas
    showLists: 'Mostrar listas de links',
    listsColumns: 'Número de colunas',
    listsBackgroundColor: 'Cor de fundo das listas',
    listsBackdropBlur: 'Desfoque de fundo das listas',
    addNewList: 'Adicionar nova lista',
    
    // Plano de fundo
    backgroundType: 'Tipo de plano de fundo',
    solidColor: 'Cor sólida',
    gradient: 'Gradiente',
    image: 'Imagem',
    brightness: 'Brilho',
    contrast: 'Contraste',
    saturation: 'Saturação',
    blur: 'Desfoque',
    hueRotate: 'Rotação de matiz',
    sepia: 'Sépia',
    grayscale: 'Escala de cinza',
    invert: 'Inverter',
    shadowOverlay: 'Sobreposição de sombra',
    parallaxEffect: 'Efeito parallax',
    autoSwitch: 'Troca automática',
    switchInterval: 'Intervalo de troca',
    addImages: 'Adicionar imagens',
    uploadImages: 'Enviar imagens',
    
    // Predefinições
    presets: 'Predefinições',
    createPreset: 'Criar predefinição',
    presetName: 'Nome da predefinição',
    noPresets: 'Nenhuma predefinição criada. Crie sua primeira predefinição para alternar rapidamente entre configurações.',
    renamePreset: 'Renomear predefinição',
    updatePreset: 'Atualizar predefinição com configurações atuais',
    deletePreset: 'Excluir predefinição',
    
    // Traduções
    aiTranslationsDisclaimer: 'Todas as traduções são geradas por IA',

    // Redefinir e exportar
    resetAllColors: 'Redefinir todas as cores para destaque',
    resetAllSettings: 'Redefinir todas as configurações',
  },

  // Listas
  lists: {
    newList: 'Nova lista',
    listName: 'Nome da lista',
    addLink: 'Adicionar link',
    linkName: 'Nome do link',
    linkUrl: 'URL do link',
    editList: 'Editar lista',
    deleteList: 'Excluir lista',
    listIcon: 'Ícone da lista',
    listColor: 'Cor da lista',
    hideIcons: 'Ocultar ícones dos links',
    openInNewWindow: 'Abrir em nova janela',
    copyLink: 'Copiar link',
    editLink: 'Editar link',
    deleteLink: 'Excluir link',
  },

  // Links rápidos
  fastLinks: {
    newFastLink: 'Novo link rápido',
    fastLinkName: 'Nome',
    fastLinkUrl: 'URL',
    editFastLink: 'Editar link rápido',
    deleteFastLink: 'Excluir link rápido',
    fastLinkColor: 'Cor do link rápido',
  },

  // Busca
  search: {
    placeholder: 'Buscar...',
    searchWith: 'Buscar com',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
  },

  // Erros e notificações
  errors: {
    invalidUrl: 'URL inválida',
    nameRequired: 'Nome é obrigatório',
    urlRequired: 'URL é obrigatória',
    fileUploadError: 'Erro no envio do arquivo',
    settingsImportError: 'Erro na importação das configurações',
    settingsExportError: 'Erro na exportação das configurações',
    criticalError: 'Ocorreu um erro crítico. A página será recarregada.',
    jsError: 'Erro JavaScript',
    promiseRejection: 'Erro Promise não tratado',
  },

  // Dicas
  tooltips: {
    settings: 'Configurações',
    addList: 'Adicionar lista',
    addFastLink: 'Adicionar link rápido',
    editItem: 'Editar',
    deleteItem: 'Excluir',
    updateItem: 'Atualizar',
    resetColor: 'Redefinir cor',
    openLink: 'Abrir link',
    copyLink: 'Copiar link',
    dragToReorder: 'Arrastar para reordenar',
    exportSettings: 'Exportar todas as configurações para arquivo',
    importSettings: 'Importar configurações de arquivo',
  },

  // Tempo e data
  time: {
    seconds: 'seg',
    minutes: 'min',
    hours: 'h',
    days: 'd',
    months: ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'],
    weekdays: ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'],
  },

  // Unidades e tamanhos
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'seg',
    minutes: 'min',
    small: 'Pequeno',
    medium: 'Médio',
    large: 'Grande',
  },
};
