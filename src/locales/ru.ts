// Русский словарь переводов
export const ru = {
  // Общие
  common: {
    apply: 'Применить',
    cancel: 'Отмена',
    save: 'Сохранить',
    delete: 'Удалить',
    edit: 'Редактировать',
    update: 'Обновить',
    reset: 'Сбросить',
    resetIcon: 'Сбросить иконку',
    add: 'Добавить',
    remove: 'Удалить',
    close: 'Закрыть',
    open: 'Открыть',
    enable: 'Включить',
    disable: 'Отключить',
    show: 'Показать',
    hide: 'Скрыть',
    yes: 'Да',
    no: 'Нет',
    loading: 'Загрузка...',
    error: 'Ошибка',
    success: 'Успешно',
    warning: 'Предупреждение',
    info: 'Информация',
    create: 'Создать',
    enterName: 'Введите название...',
  },

  // Настройки
  settings: {
    title: 'Настройки',
    basicSettings: 'Основные настройки',
    clockSettings: 'Настройки часов',
    searchSettings: 'Поисковик',
    fastLinksSettings: 'Быстрые ссылки',
    backgroundSettings: 'Фон',
    presetsSettings: 'Пресеты',
    listsSettings: 'Настройки списков',

    // Основные настройки
    language: 'Язык интерфейса',
    exportSettings: 'Экспорт',
    importSettings: 'Импорт',
    theme: 'Тема',
    accentColor: 'Акцентный цвет',
    borderRadius: 'Радиус границ',
    cleanMode: 'Чистый режим',
    cleanModeDescription: 'Скрывает кнопки открытия списков',
    colorScheme: 'Цветовая схема',
    borderRounding: 'Скругление элементов',
    
    // Часы
    showClock: 'Показывать часы',
    showSeconds: 'Показывать секунды',
    showDate: 'Показывать дату',
    clockSize: 'Размер часов',
    clockColor: 'Цвет часов и даты',

    // Поиск
    showSearch: 'Показывать поисковик',
    searchEngine: 'Поисковая система',
    searchSize: 'Размер поисковика',
    searchBackgroundColor: 'Цвет фона поисковика',
    searchBorderColor: 'Цвет границы поисковика',
    searchTextColor: 'Цвет текста поисковика',
    searchBackdropBlur: 'Размытие фона поисковика',
    showSearchEngine: 'Показать поисковик',
    backgroundBlur: 'Размытие фона',
    
    // Быстрые ссылки
    showFastLinks: 'Показывать быстрые ссылки',
    fastLinksColumns: 'Количество колонок',
    fastLinksSize: 'Размер быстрых ссылок',
    fastLinksBackdropBlur: 'Размытие фона быстрых ссылок',
    addFastLink: 'Добавить быструю ссылку',
    textColor: 'Цвет текста заголовков',
    backdropColor: 'Цвет задника',
    iconBackgroundColor: 'Цвет фона иконки',
    colors: 'Цвета',
    display: 'Отображение',
    hideIcons: 'Скрыть иконки',
    hideText: 'Скрыть текст',
    backgroundEffects: 'Эффекты фона иконок',

    // Списки
    showLists: 'Показывать списки ссылок',
    listsColumns: 'Количество колонок',
    listsBackgroundColor: 'Цвет фона списков',
    listsBackdropBlur: 'Размытие фона списков',
    addNewList: 'Добавить новый список',
    listBackground: 'Фон списков',
    backgroundColor: 'Цвет фона',
    borderColor: 'Цвет границы',
    borderThickness: 'Толщина границы',
    hideBorder: 'Скрыть границу',
    hideBackground: 'Скрыть фон',
    separator: 'Разделитель',
    separatorColor: 'Цвет разделителя',
    separatorThickness: 'Толщина разделителя',
    hideSeparator: 'Скрыть разделитель',
    colorsAndIcons: 'Цвета и иконки',
    titleColor: 'Цвет заголовков',
    linkColor: 'Цвет ссылок',
    hideLinkIcons: 'Скрыть иконки ссылок',
    listGrid: 'Сетка списков',
    columnsCount: 'Количество колонок',
    listManagement: 'Управление списками',
    
    // Фон
    backgroundType: 'Тип фона',
    solidColor: 'Сплошной цвет',
    gradient: 'Градиент',
    image: 'Изображение',
    addImage: 'Добавить изображение',
    checking: 'Проверка...',
    addRandomPhoto: 'Добавить случайное фото из интернета',
    parallaxEffect: 'Эффект параллакса',
    parallaxDescription: 'Фоновое изображение будет следовать за движением мышки',
    shadowBottom: 'Затенение снизу',
    shadowDescription: 'Градиентное затенение для лучшей видимости списков',
    intensity: 'Интенсивность:',
    height: 'Высота:',
    linear: 'Линейный',
    radial: 'Радиальный',
    gallery: 'Галерея',
    startAutoSwitch: 'Запустить автоматическое переключение',
    stopAutoSwitch: 'Остановить автоматическое переключение',
    onLoad: 'При загрузке',
    daily: 'Раз в день',
    deleteImage: 'Удалить изображение',
    gradientType: 'Тип градиента',
    addColor: 'Добавить цвет',
    deleteColor: 'Удалить цвет',
    direction: 'Направление',
    position: 'Позиция',
    customCSS: 'CSS градиент (необязательно)',
    customCSSDescription: 'Введите CSS строку градиента для применения вместо ручной настройки',
    backgroundFilters: 'Фильтры фона',
    resetFilters: 'Сбросить фильтры',
    expandFilters: 'Развернуть фильтры',
    collapseFilters: 'Свернуть фильтры',
    blur: 'Размытие',
    brightness: 'Яркость',
    contrast: 'Контрастность',
    saturation: 'Насыщенность',
    hueRotate: 'Поворот оттенка',
    sepia: 'Сепия',
    grayscale: 'Черно-белое',
    invert: 'Инверсия',
    color: 'Цвет',

    shadowOverlay: 'Теневое наложение',
    autoSwitch: 'Автопереключение',
    switchInterval: 'Интервал переключения',
    addImages: 'Добавить изображения',
    uploadImages: 'Загрузить изображения',
    filters: 'Фильтры фона',
    font: 'Шрифт',
    right: 'Вправо',
    left: 'Влево',
    bottom: 'Вниз',
    top: 'Вверх',
    bottomRight: 'Вниз-вправо',
    bottomLeft: 'Вниз-влево',
    topRight: 'Вверх-вправо',
    topLeft: 'Вверх-влево',
    center: 'Центр',

    // Пресеты
    presets: 'Пресеты',
    createPreset: 'Создать пресет',
    presetName: 'Название пресета',
    noPresets: 'Пресеты не созданы. Создайте первый пресет для быстрого переключения между настройками.',
    renamePreset: 'Переименовать пресет',
    updatePreset: 'Обновить пресет текущими настройками',
    deletePreset: 'Удалить пресет',
    createNewPreset: 'Создать новый пресет',
    presetDescription: 'Пресет сохранит текущие настройки цветов, шрифтов и фона.',

    // Категории шрифтов
    fontCategories: {
      sansSerif: 'Без засечек',
      serif: 'С засечками',
      monospace: 'Моноширинные',
      display: 'Декоративные',
      handwriting: 'Рукописные',
      pixel: 'Пиксельные',
      terminal: 'Терминальные'
    },

    // Сброс и экспорт
    resetAllColors: 'Сбросить все цвета к акцентному',
    resetAllSettings: 'Сбросить все настройки',
  },

  // Списки
  lists: {
    newList: 'Новый список',
    listName: 'Название списка',
    addLink: 'Добавить ссылку',
    linkName: 'Название ссылки',
    linkUrl: 'URL ссылки',
    editList: 'Редактировать список',
    deleteList: 'Удалить список',
    listIcon: 'Иконка списка',
    listColor: 'Цвет списка',
    hideIcons: 'Скрыть иконки ссылок',
    openInNewWindow: 'Открыть в новом окне',
    copyLink: 'Копировать ссылку',
    editLink: 'Редактировать ссылку',
    deleteLink: 'Удалить ссылку',
    title: 'Название',
    url: 'URL',
    addNewList: 'Добавить список',
    iconColor: 'Цвет иконки',
  },

  // Диалоги и формы
  dialogs: {
    newName: 'Новое название',
    newListName: 'Новое название списка',
    linkTitle: 'Название ссылки',
    linkColor: 'Цвет ссылки',
    separatorColor: 'Цвет разделителя',
    titleColor: 'Цвет заголовка',
  },

  // Быстрые ссылки
  fastLinks: {
    newFastLink: 'Новая быстрая ссылка',
    fastLinkName: 'Название быстрой ссылки',
    fastLinkUrl: 'URL',
    editFastLink: 'Редактировать быструю ссылку',
    deleteFastLink: 'Удалить быструю ссылку',
    fastLinkColor: 'Цвет быстрой ссылки',
  },

  // Поиск
  search: {
    placeholder: 'Поиск...',
    searchWith: 'Искать с помощью',
    google: 'Google',
    yandex: 'Яндекс',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
    searchFonts: 'Поиск шрифтов...',
    fontsNotFound: 'Шрифты не найдены',
  },

  // Ошибки и уведомления
  errors: {
    invalidUrl: 'Неверный URL',
    nameRequired: 'Название обязательно',
    urlRequired: 'URL обязателен',
    fileUploadError: 'Ошибка загрузки файла',
    settingsImportError: 'Ошибка импорта настроек',
    settingsExportError: 'Ошибка экспорта настроек',
    invalidImageUrl: 'Недопустимый URL изображения',
    imageValidationError: 'Ошибка при проверке изображения',
    settingsImported: 'Настройки успешно импортированы! Страница будет перезагружена для применения всех изменений.',
    settingsExported: 'Настройки экспортированы успешно',
    parseError: 'Ошибка при парсинге файла:',
    invalidFileFormat: 'Ошибка при чтении файла настроек. Убедитесь, что файл имеет правильный формат.',
    importError: 'Ошибка при импорте настроек. Проверьте консоль для подробностей.',
    exportError: 'Ошибка при экспорте настроек. Проверьте консоль для подробностей.',
    resetConfirm: 'Вы уверены, что хотите сбросить все настройки к стандартным значениям? Это действие нельзя отменить. Все пользовательские настройки, включая фоны, списки и быстрые ссылки, будут полностью удалены из localStorage.',
    deleteListConfirm: 'Вы уверены, что хотите удалить этот список? Это действие нельзя отменить.',
  },

  // Подсказки
  tooltips: {
    settings: 'Настройки',
    addList: 'Добавить список',
    addFastLink: 'Добавить быструю ссылку',
    editItem: 'Редактировать',
    deleteItem: 'Удалить',
    updateItem: 'Обновить',
    resetColor: 'Сбросить цвет',
    openLink: 'Открыть ссылку',
    copyLink: 'Копировать ссылку',
    dragToReorder: 'Перетащите для изменения порядка',
    exportSettings: 'Экспортировать все настройки в файл',
    importSettings: 'Импортировать настройки из файла',
    resetAllSettings: 'Сбросить все настройки',
    closeSettings: 'Закрыть настройки',
    generateColor: 'Генерация цвета на основе изображения',
    applyAccentColor: 'Применить акцентный цвет ко всем элементам',
    addRandomPhoto: 'Добавить случайное фото из интернета',
    deleteImage: 'Удалить изображение',
    addColor: 'Добавить цвет',
    deleteColor: 'Удалить цвет',
    resetFilters: 'Сбросить фильтры',
    expandFilters: 'Развернуть фильтры',
    collapseFilters: 'Свернуть фильтры',
    stopAutoSwitch: 'Остановить автоматическое переключение',
    startAutoSwitch: 'Запустить автоматическое переключение',
  },

  // Aria-labels
  ariaLabels: {
    settings: 'Настройки',
    resetAllSettings: 'Сбросить все настройки',
    closeSettings: 'Закрыть настройки',
    applyAccentColor: 'Применить акцентный цвет ко всем элементам',
    addList: 'Добавить список',
    addRandomPhoto: 'Добавить случайное фото',
    deleteImage: 'Удалить изображение',
    addColor: 'Добавить цвет',
    deleteColor: 'Удалить цвет',
    resetFilters: 'Сбросить фильтры',
    expandFilters: 'Развернуть фильтры',
    collapseFilters: 'Свернуть фильтры',
  },

  // Радиус скругления
  radius: {
    none: 'Без скругления',
    small: 'Малое',
    medium: 'Среднее',
    large: 'Большое',
    full: 'Полное',
  },

  // Время и дата
  time: {
    seconds: 'сек',
    minutes: 'мин',
    hours: 'ч',
    days: 'дн',
    months: ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь'],
    weekdays: ['Воскресенье', 'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота'],
  },

  // Размеры и единицы
  units: {
    pixels: 'пикс',
    percent: '%',
    seconds: 'сек',
    minutes: 'мин',
    small: 'Маленький',
    medium: 'Средний',
    large: 'Большой',
  },
};

export type TranslationKeys = typeof ru;
