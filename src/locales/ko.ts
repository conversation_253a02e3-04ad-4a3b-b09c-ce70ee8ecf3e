export const ko = {
  // 공통
  common: {
    apply: '적용',
    cancel: '취소',
    save: '저장',
    delete: '삭제',
    edit: '편집',
    update: '업데이트',
    reset: '재설정',
    add: '추가',
    remove: '제거',
    close: '닫기',
    open: '열기',
    enable: '활성화',
    disable: '비활성화',
    show: '표시',
    hide: '숨기기',
    yes: '예',
    no: '아니오',
    loading: '로딩 중...',
    error: '오류',
    success: '성공',
    warning: '경고',
    info: '정보',
  },

  // 설정
  settings: {
    title: '설정',
    basicSettings: '기본 설정',
    clockSettings: '시계 설정',
    searchSettings: '검색 엔진',
    fastLinksSettings: '빠른 링크',
    backgroundSettings: '배경',
    presetsSettings: '프리셋',
    listsSettings: '목록 설정',
    
    // 기본 설정
    language: '인터페이스 언어',
    exportSettings: '내보내기',
    importSettings: '가져오기',
    theme: '테마',
    accentColor: '강조 색상',
    borderRadius: '테두리 반경',
    cleanMode: '깔끔한 모드',
    cleanModeDescription: '목록 열기 버튼 숨기기',
    
    // 시계
    showClock: '시계 표시',
    showSeconds: '초 표시',
    showDate: '날짜 표시',
    clockSize: '시계 크기',
    clockColor: '시계 및 날짜 색상',
    
    // 검색
    showSearch: '검색 표시',
    searchEngine: '검색 엔진',
    searchSize: '검색 크기',
    searchBackgroundColor: '검색 배경 색상',
    searchBorderColor: '검색 테두리 색상',
    searchTextColor: '검색 텍스트 색상',
    searchBackdropBlur: '검색 배경 흐림',
    
    // 빠른 링크
    showFastLinks: '빠른 링크 표시',
    fastLinksColumns: '열 수',
    fastLinksSize: '빠른 링크 크기',
    fastLinksBackdropBlur: '빠른 링크 배경 흐림',
    addFastLink: '빠른 링크 추가',
    
    // 목록
    showLists: '링크 목록 표시',
    listsColumns: '열 수',
    listsBackgroundColor: '목록 배경 색상',
    listsBackdropBlur: '목록 배경 흐림',
    addNewList: '새 목록 추가',
    
    // 배경
    backgroundType: '배경 유형',
    solidColor: '단색',
    gradient: '그라데이션',
    image: '이미지',
    brightness: '밝기',
    contrast: '대비',
    saturation: '채도',
    blur: '흐림',
    hueRotate: '색조 회전',
    sepia: '세피아',
    grayscale: '회색조',
    invert: '반전',
    shadowOverlay: '그림자 오버레이',
    parallaxEffect: '패럴랙스 효과',
    autoSwitch: '자동 전환',
    switchInterval: '전환 간격',
    addImages: '이미지 추가',
    uploadImages: '이미지 업로드',
    
    // 프리셋
    presets: '프리셋',
    createPreset: '프리셋 생성',
    presetName: '프리셋 이름',
    noPresets: '생성된 프리셋이 없습니다. 설정을 빠르게 전환하기 위해 첫 번째 프리셋을 생성하세요.',
    renamePreset: '프리셋 이름 변경',
    updatePreset: '현재 설정으로 프리셋 업데이트',
    deletePreset: '프리셋 삭제',
    
    // 재설정 및 내보내기
    resetAllColors: '모든 색상을 강조 색상으로 재설정',
    resetAllSettings: '모든 설정 재설정',
  },

  // 목록
  lists: {
    newList: '새 목록',
    listName: '목록 이름',
    addLink: '링크 추가',
    linkName: '링크 이름',
    linkUrl: '링크 URL',
    editList: '목록 편집',
    deleteList: '목록 삭제',
    listIcon: '목록 아이콘',
    listColor: '목록 색상',
    hideIcons: '링크 아이콘 숨기기',
    openInNewWindow: '새 창에서 열기',
    copyLink: '링크 복사',
    editLink: '링크 편집',
    deleteLink: '링크 삭제',
  },

  // 빠른 링크
  fastLinks: {
    newFastLink: '새 빠른 링크',
    fastLinkName: '이름',
    fastLinkUrl: 'URL',
    editFastLink: '빠른 링크 편집',
    deleteFastLink: '빠른 링크 삭제',
    fastLinkColor: '빠른 링크 색상',
  },

  // 검색
  search: {
    placeholder: '검색...',
    searchWith: '검색 엔진',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
  },

  // 오류 및 알림
  errors: {
    invalidUrl: '잘못된 URL',
    nameRequired: '이름이 필요합니다',
    urlRequired: 'URL이 필요합니다',
    fileUploadError: '파일 업로드 오류',
    settingsImportError: '설정 가져오기 오류',
    settingsExportError: '설정 내보내기 오류',
  },

  // 툴팁
  tooltips: {
    settings: '설정',
    addList: '목록 추가',
    addFastLink: '빠른 링크 추가',
    editItem: '편집',
    deleteItem: '삭제',
    updateItem: '업데이트',
    resetColor: '색상 재설정',
    openLink: '링크 열기',
    copyLink: '링크 복사',
    dragToReorder: '드래그하여 순서 변경',
    exportSettings: '모든 설정을 파일로 내보내기',
    importSettings: '파일에서 설정 가져오기',
  },

  // 시간 및 날짜
  time: {
    seconds: '초',
    minutes: '분',
    hours: '시간',
    days: '일',
    months: ['1월', '2월', '3월', '4월', '5월', '6월', '7월', '8월', '9월', '10월', '11월', '12월'],
    weekdays: ['일요일', '월요일', '화요일', '수요일', '목요일', '금요일', '토요일'],
  },

  // 단위 및 크기
  units: {
    pixels: 'px',
    percent: '%',
    seconds: '초',
    minutes: '분',
    small: '작음',
    medium: '중간',
    large: '큼',
  },
};
