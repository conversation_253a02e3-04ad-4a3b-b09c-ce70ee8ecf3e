export const pl = {
  // Wspólne
  common: {
    apply: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    cancel: 'Anuluj',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    delete: '<PERSON>u<PERSON>',
    edit: 'Edytuj',
    update: 'Aktualizuj',
    reset: 'Resetuj',
    add: 'Doda<PERSON>',
    remove: '<PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    open: 'Otw<PERSON><PERSON>',
    enable: '<PERSON><PERSON><PERSON><PERSON>',
    disable: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    show: '<PERSON><PERSON><PERSON>',
    hide: '<PERSON><PERSON><PERSON><PERSON>',
    yes: 'Tak',
    no: 'Nie',
    loading: 'Ładowanie...',
    error: 'Błąd',
    success: 'Sukces',
    warning: 'Ostrzeżenie',
    info: 'Informacja',
  },

  // Ustawienia
  settings: {
    title: 'Ustawienia',
    basicSettings: 'Podstawowe ustawienia',
    clockSettings: 'Ustawienia zegara',
    searchSettings: 'Wyszukiwarka',
    fastLinksSettings: '<PERSON><PERSON><PERSON><PERSON> linki',
    backgroundSettings: 'Tło',
    presetsSettings: 'Predefiniowane',
    listsSettings: 'Ustawienia list',
    
    // Podstawowe ustawienia
    language: 'Język interfejsu',
    exportSettings: 'Eksportuj',
    importSettings: 'Importuj',
    theme: 'Motyw',
    accentColor: 'Kolor akcentu',
    borderRadius: 'Promień obramowania',
    cleanMode: 'Tryb czysty',
    cleanModeDescription: 'Ukrywa przyciski otwierania list',
    
    // Zegar
    showClock: 'Pokaż zegar',
    showSeconds: 'Pokaż sekundy',
    showDate: 'Pokaż datę',
    clockSize: 'Rozmiar zegara',
    clockColor: 'Kolor zegara i daty',
    
    // Wyszukiwanie
    showSearch: 'Pokaż wyszukiwanie',
    searchEngine: 'Wyszukiwarka',
    searchSize: 'Rozmiar wyszukiwania',
    searchBackgroundColor: 'Kolor tła wyszukiwania',
    searchBorderColor: 'Kolor obramowania wyszukiwania',
    searchTextColor: 'Kolor tekstu wyszukiwania',
    searchBackdropBlur: 'Rozmycie tła wyszukiwania',
    
    // Szybkie linki
    showFastLinks: 'Pokaż szybkie linki',
    fastLinksColumns: 'Liczba kolumn',
    fastLinksSize: 'Rozmiar szybkich linków',
    fastLinksBackdropBlur: 'Rozmycie tła szybkich linków',
    addFastLink: 'Dodaj szybki link',
    
    // Listy
    showLists: 'Pokaż listy linków',
    listsColumns: 'Liczba kolumn',
    listsBackgroundColor: 'Kolor tła list',
    listsBackdropBlur: 'Rozmycie tła list',
    addNewList: 'Dodaj nową listę',
    
    // Tło
    backgroundType: 'Typ tła',
    solidColor: 'Jednolity kolor',
    gradient: 'Gradient',
    image: 'Obraz',
    brightness: 'Jasność',
    contrast: 'Kontrast',
    saturation: 'Nasycenie',
    blur: 'Rozmycie',
    hueRotate: 'Obrót odcienia',
    sepia: 'Sepia',
    grayscale: 'Skala szarości',
    invert: 'Odwróć',
    shadowOverlay: 'Nakładka cienia',
    parallaxEffect: 'Efekt paralaksy',
    autoSwitch: 'Automatyczne przełączanie',
    switchInterval: 'Interwał przełączania',
    addImages: 'Dodaj obrazy',
    uploadImages: 'Prześlij obrazy',
    
    // Predefiniowane
    presets: 'Predefiniowane',
    createPreset: 'Utwórz predefiniowane',
    presetName: 'Nazwa predefiniowanego',
    noPresets: 'Nie utworzono predefiniowanych. Utwórz swoje pierwsze predefiniowane, aby szybko przełączać się między ustawieniami.',
    renamePreset: 'Zmień nazwę predefiniowanego',
    updatePreset: 'Aktualizuj predefiniowane z bieżącymi ustawieniami',
    deletePreset: 'Usuń predefiniowane',
    
    // Tłumaczenia
    aiTranslationsDisclaimer: 'Wszystkie tłumaczenia są generowane przez AI',

    // Resetuj i eksportuj
    resetAllColors: 'Resetuj wszystkie kolory do akcentu',
    resetAllSettings: 'Resetuj wszystkie ustawienia',
  },

  // Listy
  lists: {
    newList: 'Nowa lista',
    listName: 'Nazwa listy',
    addLink: 'Dodaj link',
    linkName: 'Nazwa linku',
    linkUrl: 'URL linku',
    editList: 'Edytuj listę',
    deleteList: 'Usuń listę',
    listIcon: 'Ikona listy',
    listColor: 'Kolor listy',
    hideIcons: 'Ukryj ikony linków',
    openInNewWindow: 'Otwórz w nowym oknie',
    copyLink: 'Kopiuj link',
    editLink: 'Edytuj link',
    deleteLink: 'Usuń link',
  },

  // Szybkie linki
  fastLinks: {
    newFastLink: 'Nowy szybki link',
    fastLinkName: 'Nazwa',
    fastLinkUrl: 'URL',
    editFastLink: 'Edytuj szybki link',
    deleteFastLink: 'Usuń szybki link',
    fastLinkColor: 'Kolor szybkiego linku',
  },

  // Wyszukiwanie
  search: {
    placeholder: 'Szukaj...',
    searchWith: 'Szukaj z',
    google: 'Google',
    yandex: 'Yandex',
    bing: 'Bing',
    duckduckgo: 'DuckDuckGo',
  },

  // Błędy i powiadomienia
  errors: {
    invalidUrl: 'Nieprawidłowy URL',
    nameRequired: 'Nazwa jest wymagana',
    urlRequired: 'URL jest wymagany',
    fileUploadError: 'Błąd przesyłania pliku',
    settingsImportError: 'Błąd importu ustawień',
    settingsExportError: 'Błąd eksportu ustawień',
    criticalError: 'Wystąpił błąd krytyczny. Strona zostanie przeładowana.',
    jsError: 'Błąd JavaScript',
    promiseRejection: 'Nieobsłużony błąd Promise',
  },

  // Podpowiedzi
  tooltips: {
    settings: 'Ustawienia',
    addList: 'Dodaj listę',
    addFastLink: 'Dodaj szybki link',
    editItem: 'Edytuj',
    deleteItem: 'Usuń',
    updateItem: 'Aktualizuj',
    resetColor: 'Resetuj kolor',
    openLink: 'Otwórz link',
    copyLink: 'Kopiuj link',
    dragToReorder: 'Przeciągnij, aby zmienić kolejność',
    exportSettings: 'Eksportuj wszystkie ustawienia do pliku',
    importSettings: 'Importuj ustawienia z pliku',
  },

  // Czas i data
  time: {
    seconds: 'sek',
    minutes: 'min',
    hours: 'godz',
    days: 'dni',
    months: ['Styczeń', 'Luty', 'Marzec', 'Kwiecień', 'Maj', 'Czerwiec', 'Lipiec', 'Sierpień', 'Wrzesień', 'Październik', 'Listopad', 'Grudzień'],
    weekdays: ['Niedziela', 'Poniedziałek', 'Wtorek', 'Środa', 'Czwartek', 'Piątek', 'Sobota'],
  },

  // Jednostki i rozmiary
  units: {
    pixels: 'px',
    percent: '%',
    seconds: 'sek',
    minutes: 'min',
    small: 'Mały',
    medium: 'Średni',
    large: 'Duży',
  },
};
