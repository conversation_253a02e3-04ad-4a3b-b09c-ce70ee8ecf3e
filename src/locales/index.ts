import { ru, TranslationKeys } from './ru';
import { en } from './en';
import { de } from './de';
import { fr } from './fr';
import { es } from './es';
import { it } from './it';
import { pt } from './pt';
import { nl } from './nl';
import { pl } from './pl';
import { cs } from './cs';
import { ja } from './ja';
import { ko } from './ko';

// Доступные языки
export const availableLanguages = {
  ru: 'Русский',
  en: 'English',
  de: 'Deutsch',
  fr: 'Français',
  es: 'Español',
  it: 'Italiano',
  pt: 'Português',
  nl: 'Nederlands',
  pl: 'Polski',
  cs: 'Čeština',
  ja: '日本語',
  ko: '한국어',
} as const;

export type LanguageCode = keyof typeof availableLanguages;

// Словари переводов
const translations: Record<LanguageCode, TranslationKeys> = {
  ru,
  en: en as TranslationKeys,
  de: de as TranslationKeys,
  fr: fr as TranslationKeys,
  es: es as TranslationKeys,
  it: it as TranslationKeys,
  pt: pt as TranslationKeys,
  nl: nl as TranslationKeys,
  pl: pl as TranslationKeys,
  cs: cs as TranslationKeys,
  ja: ja as TranslationKeys,
  ko: ko as TranslationKeys,
};

// Текущий язык (по умолчанию русский)
let currentLanguage: LanguageCode = 'ru';

// Функция для установки языка
export const setLanguage = (language: LanguageCode) => {
  currentLanguage = language;
  // Сохраняем выбранный язык в localStorage
  localStorage.setItem('language', language);
};

// Функция для получения текущего языка
export const getCurrentLanguage = (): LanguageCode => {
  // Пытаемся загрузить язык из localStorage
  const savedLanguage = localStorage.getItem('language') as LanguageCode;
  if (savedLanguage && savedLanguage in availableLanguages) {
    currentLanguage = savedLanguage;
  }
  return currentLanguage;
};

// Функция для получения перевода по ключу
export const t = (key: string): string => {
  const keys = key.split('.');
  let value: any = translations[getCurrentLanguage()];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Если перевод не найден, возвращаем ключ
      console.warn(`Translation not found for key: ${key}`);
      return key;
    }
  }
  
  return typeof value === 'string' ? value : key;
};

// Хук для использования переводов в React компонентах
import { useState, useEffect } from 'react';

export const useTranslation = () => {
  const [language, setLanguageState] = useState<LanguageCode>(getCurrentLanguage());
  
  const changeLanguage = (newLanguage: LanguageCode) => {
    setLanguage(newLanguage);
    setLanguageState(newLanguage);
  };
  
  useEffect(() => {
    // Инициализируем язык при загрузке
    const savedLanguage = getCurrentLanguage();
    setLanguageState(savedLanguage);
  }, []);
  
  return {
    t,
    language,
    changeLanguage,
    availableLanguages,
  };
};

// Экспортируем основные функции
export default translations;
