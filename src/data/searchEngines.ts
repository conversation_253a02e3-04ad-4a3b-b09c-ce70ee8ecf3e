export interface SearchEngine {
  id: string;
  name: string;
  url: string;
  placeholder: string;
}

import { t } from '../locales';

export const SEARCH_ENGINES: SearchEngine[] = [
  {
    id: 'google',
    name: 'Google',
    url: 'https://www.google.com/search?q=',
    placeholder: t('search.searchInGoogle')
  },
  {
    id: 'yandex',
    name: '<PERSON>de<PERSON>',
    url: 'https://yandex.ru/search/?text=',
    placeholder: t('search.searchInYandex')
  },
  {
    id: 'bing',
    name: '<PERSON>',
    url: 'https://www.bing.com/search?q=',
    placeholder: t('search.searchInBing')
  },
  {
    id: 'duckduckgo',
    name: '<PERSON><PERSON>uckG<PERSON>',
    url: 'https://duckduckgo.com/?q=',
    placeholder: t('search.searchInDuckDuckGo')
  },
  {
    id: 'yahoo',
    name: 'Yahoo',
    url: 'https://search.yahoo.com/search?p=',
    placeholder: t('search.searchInYahoo')
  },
  {
    id: 'baidu',
    name: '<PERSON><PERSON>',
    url: 'https://www.baidu.com/s?wd=',
    placeholder: t('search.searchInBaidu')
  },
  {
    id: 'startpage',
    name: 'Startpage',
    url: 'https://www.startpage.com/sp/search?query=',
    placeholder: t('search.searchInStartpage')
  },
  {
    id: 'searx',
    name: 'SearX',
    url: 'https://searx.org/?q=',
    placeholder: t('search.searchInSearX')
  },
  {
    id: 'ecosia',
    name: 'Ecosia',
    url: 'https://www.ecosia.org/search?q=',
    placeholder: t('search.searchInEcosia')
  },
  {
    id: 'brave',
    name: 'Brave Search',
    url: 'https://search.brave.com/search?q=',
    placeholder: t('search.searchInBrave')
  }
];

export function getSearchEngine(id: string): SearchEngine {
  return SEARCH_ENGINES.find(engine => engine.id === id) || SEARCH_ENGINES[0];
}
